"""
Shared MCP Detection Utility

This module provides functions to aggregate custom MCP servers from ALL agents
belonging to a user's account, enabling shared MCP functionality across agents.

Key Features:
- Aggregates custom_mcps from all user agents (including is_default=True)
- Deduplicates MCP servers by name and type
- Maintains compatibility with existing MCP configuration format
- Supports both Supabase client and account_id-based queries
"""

import logging
from typing import List, Dict, Any, Optional, Set, Tuple
from supabase import Client

logger = logging.getLogger(__name__)


async def get_all_user_custom_mcps(
    client: Client, 
    account_id: str
) -> List[Dict[str, Any]]:
    """
    Get all custom MCPs from ALL agents belonging to the user's account.
    
    This function aggregates custom_mcps from every agent in the account,
    including the default agent and any custom agents the user has created.
    
    Args:
        client: Supabase client instance
        account_id: The account ID to query agents for
        
    Returns:
        List of deduplicated custom MCP configurations from all user agents
        
    Example return format:
    [
        {
            "name": "Gmail",
            "type": "http", 
            "config": {"url": "https://mcp.composio.dev/..."},
            "enabledTools": ["gmail_send", "gmail_read"],
            "source_agent_id": "uuid-of-agent-that-has-this-mcp"
        }
    ]
    """
    try:
        logger.info(f"Fetching all custom MCPs for account {account_id}")
        
        # Query ALL agents for this account and get their custom_mcps
        agents_result = (
            await client.table("agents")
            .select("agent_id, name, custom_mcps, is_default")
            .eq("account_id", account_id)
            .execute()
        )
        
        if not agents_result.data:
            logger.info(f"No agents found for account {account_id}")
            return []
        
        # Aggregate all custom MCPs from all agents
        all_custom_mcps = []
        seen_mcps: Set[Tuple[str, str]] = set()  # Track (name, type) pairs to deduplicate
        
        for agent in agents_result.data:
            agent_id = agent["agent_id"]
            agent_name = agent["name"]
            is_default = agent.get("is_default", False)
            custom_mcps = agent.get("custom_mcps", [])
            
            if not custom_mcps:
                continue
                
            logger.info(
                f"Found {len(custom_mcps)} custom MCPs in agent '{agent_name}' "
                f"({'default' if is_default else 'custom'} agent)"
            )
            
            for mcp in custom_mcps:
                mcp_name = mcp.get("name", "")
                mcp_type = mcp.get("type", "")
                
                if not mcp_name or not mcp_type:
                    logger.warning(f"Skipping invalid MCP in agent {agent_name}: {mcp}")
                    continue
                
                # Create unique identifier for deduplication
                mcp_key = (mcp_name.lower(), mcp_type.lower())
                
                if mcp_key in seen_mcps:
                    logger.debug(
                        f"Skipping duplicate MCP '{mcp_name}' ({mcp_type}) "
                        f"from agent '{agent_name}'"
                    )
                    continue
                
                # Add source agent information for debugging/tracking
                mcp_with_source = {
                    **mcp,
                    "source_agent_id": agent_id,
                    "source_agent_name": agent_name,
                    "source_is_default": is_default
                }
                
                all_custom_mcps.append(mcp_with_source)
                seen_mcps.add(mcp_key)
                
                logger.debug(
                    f"Added MCP '{mcp_name}' ({mcp_type}) from agent '{agent_name}'"
                )
        
        logger.info(
            f"Aggregated {len(all_custom_mcps)} unique custom MCPs from "
            f"{len(agents_result.data)} agents for account {account_id}"
        )
        
        return all_custom_mcps
        
    except Exception as e:
        logger.error(f"Error fetching shared custom MCPs for account {account_id}: {e}")
        return []


async def get_all_user_custom_mcps_by_user_id(
    client: Client, 
    user_id: str
) -> List[Dict[str, Any]]:
    """
    Get all custom MCPs from ALL agents belonging to the user (by user_id).
    
    This is a convenience wrapper that first resolves the user_id to account_id
    and then calls get_all_user_custom_mcps.
    
    Args:
        client: Supabase client instance
        user_id: The user ID to resolve to account_id
        
    Returns:
        List of deduplicated custom MCP configurations from all user agents
    """
    try:
        # Get account_id from user_id using basejump schema
        account_result = (
            await client.schema("basejump")
            .table("accounts")
            .select("id")
            .eq("primary_owner_user_id", user_id)
            .eq("personal_account", True)
            .execute()
        )
        
        if not account_result.data:
            logger.warning(f"No personal account found for user {user_id}")
            return []
        
        account_id = account_result.data[0]["id"]
        return await get_all_user_custom_mcps(client, account_id)
        
    except Exception as e:
        logger.error(f"Error fetching shared custom MCPs for user {user_id}: {e}")
        return []


def transform_shared_mcps_to_agent_format(
    shared_mcps: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """
    Transform shared MCPs to the format expected by agent run logic.
    
    This removes the source agent metadata and ensures compatibility
    with existing MCP processing code.
    
    Args:
        shared_mcps: List of MCPs from get_all_user_custom_mcps
        
    Returns:
        List of MCPs in the standard agent custom_mcps format
    """
    transformed = []
    
    for mcp in shared_mcps:
        # Remove source agent metadata for compatibility
        clean_mcp = {
            "name": mcp.get("name"),
            "type": mcp.get("type"),
            "config": mcp.get("config", {}),
            "enabledTools": mcp.get("enabledTools", [])
        }
        transformed.append(clean_mcp)
    
    return transformed


async def get_shared_mcps_for_agent_run(
    client: Client, 
    account_id: str
) -> List[Dict[str, Any]]:
    """
    Get shared custom MCPs in the format expected by agent run logic.
    
    This is the main function that should replace individual agent
    custom_mcps queries in agent/run.py.
    
    Args:
        client: Supabase client instance
        account_id: The account ID to query agents for
        
    Returns:
        List of custom MCPs in standard agent format, ready for MCP tool wrapper
    """
    shared_mcps = await get_all_user_custom_mcps(client, account_id)
    return transform_shared_mcps_to_agent_format(shared_mcps)


async def get_shared_mcps_for_agent_run_by_user_id(
    client: Client, 
    user_id: str
) -> List[Dict[str, Any]]:
    """
    Get shared custom MCPs by user_id in the format expected by agent run logic.
    
    Args:
        client: Supabase client instance
        user_id: The user ID to resolve to account_id
        
    Returns:
        List of custom MCPs in standard agent format, ready for MCP tool wrapper
    """
    shared_mcps = await get_all_user_custom_mcps_by_user_id(client, user_id)
    return transform_shared_mcps_to_agent_format(shared_mcps)
